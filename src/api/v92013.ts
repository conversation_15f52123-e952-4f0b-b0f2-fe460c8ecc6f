import {
  Activity92013CreateOrUpdateRequest,
  Activity92013CreateOrUpdateResponse,
  Activity92013OrderDataRequest,
  Activity92013UserDetailRequest,
  Activity92013UserDetailResponse,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  IPageBdActivity92013TokenLog,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 爱马仕定制会员令牌首购
 * @summary 创建活动
 * @request POST:/92013/createActivity
 */
export const createActivity = (
  request: Activity92013CreateOrUpdateRequest,
): Promise<Activity92013CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/92013/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱马仕定制会员令牌首购
 * @summary 令牌记录
 * @request POST:/92013/data/tokenLogRecord
 */
export const dataTokenLogRecord = (request: Activity92013OrderDataRequest): Promise<IPageBdActivity92013TokenLog> => {
  return httpRequest({
    url: '/92013/data/tokenLogRecord',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱马仕定制会员令牌首购
 * @summary 导出记录
 * @request POST:/92013/data/tokenLogRecord/export
 */
export const dataTokenLogRecordExport = (request: Activity92013OrderDataRequest): Promise<void> => {
  return httpRequest({
    url: '/92013/data/tokenLogRecord/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱马仕定制会员令牌首购
 * @summary 用户记录详情
 * @request POST:/92013/data/userDetailRecord
 */
export const dataUserDetailRecord = (
  activity92013UserDetailRequest: Activity92013UserDetailRequest,
): Promise<Activity92013UserDetailResponse> => {
  return httpRequest({
    url: '/92013/data/userDetailRecord',
    method: 'post',
    data: activity92013UserDetailRequest,
  });
};

/**
 * @tags 爱马仕定制会员令牌首购
 * @summary 查询活动信息
 * @request POST:/92013/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/92013/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱马仕定制会员令牌首购
 * @summary 查询活动参与人数信息
 * @request POST:/92013/getPrizeUserInfo
 */
export const getPrizeUserInfo = (request: BaseGetActivityRequest): Promise<string> => {
  return httpRequest({
    url: '/92013/getPrizeUserInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱马仕定制会员令牌首购
 * @summary 修改活动
 * @request POST:/92013/updateActivity
 */
export const updateActivity = (
  request: Activity92013CreateOrUpdateRequest,
): Promise<Activity92013CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/92013/updateActivity',
    method: 'post',
    data: request,
  });
};
