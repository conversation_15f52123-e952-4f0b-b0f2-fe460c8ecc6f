import {
  Activity92014CreateOrUpdateRequest,
  Activity92014CreateOrUpdateResponse,
  Activity92014OrderDataRequest,
  Activity92014UserDetailRequest,
  Activity92014UserDetailResponse,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  IPageBdActivity92014TokenLog,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 爱马仕定制会员令牌复购
 * @summary 创建活动
 * @request POST:/92014/createActivity
 */
export const createActivity = (
  request: Activity92014CreateOrUpdateRequest,
): Promise<Activity92014CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/92014/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱马仕定制会员令牌复购
 * @summary 令牌记录
 * @request POST:/92014/data/tokenLogRecord
 */
export const dataTokenLogRecord = (request: Activity92014OrderDataRequest): Promise<IPageBdActivity92014TokenLog> => {
  return httpRequest({
    url: '/92014/data/tokenLogRecord',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱马仕定制会员令牌复购
 * @summary 导出记录
 * @request POST:/92014/data/tokenLogRecord/export
 */
export const dataTokenLogRecordExport = (request: Activity92014OrderDataRequest): Promise<void> => {
  return httpRequest({
    url: '/92014/data/tokenLogRecord/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱马仕定制会员令牌复购
 * @summary 用户记录详情
 * @request POST:/92014/data/userDetailRecord
 */
export const dataUserDetailRecord = (
  activity92014UserDetailRequest: Activity92014UserDetailRequest,
): Promise<Activity92014UserDetailResponse> => {
  return httpRequest({
    url: '/92014/data/userDetailRecord',
    method: 'post',
    data: activity92014UserDetailRequest,
  });
};

/**
 * @tags 爱马仕定制会员令牌复购
 * @summary 查询活动信息
 * @request POST:/92014/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/92014/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 爱马仕定制会员令牌复购
 * @summary 修改活动
 * @request POST:/92014/updateActivity
 */
export const updateActivity = (
  request: Activity92014CreateOrUpdateRequest,
): Promise<Activity92014CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/92014/updateActivity',
    method: 'post',
    data: request,
  });
};
