/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 设置模板
 */
import React from 'react';
import { CustomValue } from '../util';
// import LzTipPanel from '@/components/LzTipPanel';
// 基础信息
import Base from './components/Base';
// 大转盘信息
import Wheel from './components/Wheel';
// 获奖信息
// import Award from './components/Award';

interface Props {
  target: number;
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  handleChange: (formData: Required<CustomValue>) => void;
}

interface EventProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<CustomValue>) => void;
}

export default (props: Props) => {
  const { target, defaultValue, value, handleChange } = props;
  console.log('target:', target);
  // 各装修自组件抛出的数据
  // 只负责转发，不作处理
  const onChange = (formData): void => {
    handleChange(formData);
  };
  const eventProps: EventProps = {
    defaultValue,
    value,
    onChange,
  };
  return (
    <div>
      {<Base {...eventProps} />}
      {<Wheel {...eventProps} />}
    </div>
  );
};
