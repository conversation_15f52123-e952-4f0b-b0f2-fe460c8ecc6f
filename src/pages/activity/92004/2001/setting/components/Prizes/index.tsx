/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useEffect, useImperativeHandle, useState } from 'react';
import { Form, Field, Radio, Table, NumberPicker, Button, Dialog, Message } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { FormLayout, PageData, PRIZE_INFO, PrizeInfo } from '../../../util';
// import LzPrize from './LzPrize';
import styles from './index.module.scss';
import { activityEditDisabled, isDisableSetPrize } from '@/utils';
import { PRIZE_TYPE } from '../../../util';
import ChoosePrize from '@/components/ChoosePrizeForDZ';
import LzDialog from '@/components/LzDialog';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();
  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  const [plan, setPlan] = useState<any[]>([]);
  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  useEffect((): void => {
    PRIZE_INFO.peopleNum = 1;
    setData({
      prizeList:
        formData.isInvite === 1 ? [] : formData.prizeList[0]?.prizeType > 0 ? formData.prizeList : [{ ...PRIZE_INFO }],
    });
  }, [formData.isInvite]);

  useImperativeHandle(sRef, (): { submit: () => object | null } => ({
    submit: () => {
      console.log('prize=========');
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  // 填写金额
  const onAwardConditionsChange = (data, index): boolean | void => {
    // 更新指定index 奖品信息
    const newFormData = { ...formData };
    newFormData.prizeList[index].peopleNum = data;
    setData(newFormData);
  };
  const addPrize = () => {
    formData.prizeList.push({
      type: 0,
      ...PRIZE_INFO,
      peopleNum: formData.peopleNum,
      id: `id-${Math.random().toString(36).substr(2, 9)}`,
      sortId: formData.prizeList.length,
    });
    setData(formData);
  };
  // 修改奖项配置
  const prizeSendTypeChange = (valueData: number) => {
    // console.log(valueData, 'valueData');
    formData.winLotteryDayType = 1;
    formData.winLotteryDayCounts = 1;
    formData.winLotteryTotalType = 1;
    formData.winLotteryTotalCounts = 1;
    formData.prizeSendType = valueData;
    formData.prizeList = [];
    formData.peopleNum = 1;
    formData.prizeList.push({
      type: 0,
      ...PRIZE_INFO,
      peopleNum: formData.peopleNum,
      id: `id-${Math.random().toString(36).substr(2, 9)}`,
      sortId: formData.prizeList.length,
    });
    setData({ formData });
  };

  const onPrizeChange = (data): boolean | void => {
    if (activityEditDisabled() && data.prizeName !== '谢谢参与') {
      if (data.sendTotalCount < defaultValue.prizeList[target].sendTotalCount) {
        Message.error(`发放份数不能小于${defaultValue.prizeList[target].sendTotalCount}份`);
        return false;
      }
    }
    // 更新指定index 奖品信息
    formData.prizeList[target] = {
      ...data,
      type: 0,
      prizeKey: data.planId || data.promoId || data.prizeKey,
      prizeName: data.prizeName || data.planName,
      peopleNum: formData.prizeList[target].peopleNum,
      sortId: target,
    };
    const list = formData.prizeList.map((item) => item.prizeKey);
    if (data.prizeType === 11) {
      formData.giftSkuList = data.skuList;
    }
    setPlan(list);
    setData(formData);
    setVisible(false);
  };
  const onCancel = (): void => {
    setVisible(false);
  };
  return (
    <div>
      <LzPanel title="邀请有礼奖品设置">
        <Form {...formItemLayout} field={field}>
          <Form.Item label="邀请有礼奖品配置" required>
            <RadioGroup
              disabled
              value={formData.isInvite}
              onChange={(isInvite: number) => setData({ isInvite })}
            >
              <Radio id="1" value={1}>
                不配置
              </Radio>
              <Radio id="2" value={2}>
                配置
              </Radio>
            </RadioGroup>
          </Form.Item>
          {formData.isInvite === 2 && (
            <>
              <Form.Item label="奖项类型" required>
                <RadioGroup
                  disabled={activityEditDisabled()}
                  value={formData.prizeSendType}
                  onChange={(prizeSendType: number) => prizeSendTypeChange(prizeSendType)}
                >
                  <Radio id="0" value={1}>
                    单一型奖品配置
                  </Radio>
                  <Radio id="1" value={2}>
                    阶梯型奖品配置
                  </Radio>
                </RadioGroup>
                <div className={styles.tip}>
                  <div className={styles['gray-text']} style={{ whiteSpace: 'pre' }}>
                    备注：
                  </div>
                  <div className={styles['gray-text']}>
                    1，单一型奖品配置：活动参与用户只要多次完成任务，即可多次领取所配奖品。同一用户的奖品领取上限可配；
                    <br />
                    2，阶梯型奖品配置：可设置完成不同梯度的任务所能获得的奖品，同一奖品只能领取一次，更适合奖品价值相对较高的活动场景
                  </div>
                </div>
              </Form.Item>
              <FormItem label="奖品设置" required>
                {formData.prizeSendType === 2 && (
                  <div className={styles['tips-box']}>
                    <div className={styles['gray-text']} style={{ marginRight: '5px', whiteSpace: 'pre' }}>
                      备注
                    </div>
                    <div>
                      <div>
                        <div className={styles['gray-text']}>
                          1，如配置阶梯型多个奖品，则用户只需满足不同阶梯之间的差额，即有机会获得对应阶梯奖品；
                        </div>
                        <div className={styles['gray-text']}>
                          2，如果设置多个门槛梯度，则每个梯度的奖品最多可领取一次；
                        </div>
                        <div className={styles['gray-text']}>3，不同阶梯的邀请入会人数，需依次递增配置；</div>
                        <div className={styles['red-text']}>4，阶梯式门槛最多可配置四个；</div>
                      </div>
                    </div>
                  </div>
                )}
                <Table dataSource={formData.prizeList} style={{ marginTop: 10 }}>
                  <Table.Column
                    title="邀请入会人数"
                    cell={(_, index, row) => {
                      return (
                        <div>
                          <NumberPicker
                            onChange={(v: number) => {
                              onAwardConditionsChange(v, index);
                            }}
                            className={styles.formNumberPicker}
                            type="inline"
                            min={1}
                            precision={2}
                            max={9999999}
                            key={formData.prizeList[index]}
                            value={formData.prizeList[index].peopleNum}
                            disabled={activityEditDisabled()}
                          />
                        </div>
                      );
                    }}
                  />
                  <Table.Column
                    title="奖品名称"
                    dataIndex="prizeName"
                    cell={(_, index, row) => <div>{row.prizeName}</div>}
                  />
                  <Table.Column
                    title="奖品类型"
                    cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                    dataIndex="prizeType"
                  />
                  <Table.Column
                    title="单位数量"
                    cell={(_, index, row) => {
                      if (row.prizeType === 1) {
                        return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                      } else {
                        return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                      }
                    }}
                  />
                  {formData.prizeSendType !== 1 && (
                    <Table.Column
                      title="单份价值(元)"
                      cell={(_, index, row) => (
                        <div>
                          {PRIZE_TYPE[row.prizeType] && row.unitPrice === 0
                            ? row.unitPrice
                            : row.unitPrice
                            ? Number(row.unitPrice).toFixed(2)
                            : ''}
                        </div>
                      )}
                    />
                  )}
                  <Table.Column
                    title="发放份数"
                    cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                  />
                  <Table.Column
                    title="操作"
                    width={130}
                    cell={(val, index, _) => (
                      <FormItem style={{ marginBottom: '0' }} disabled={isDisableSetPrize(formData.prizeList, index)}>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            let row = formData.prizeList[index];
                            if (!row.prizeName) {
                              row = null;
                            }
                            setEditValue(row || null);
                            setTarget(index);
                            setVisible(true);
                          }}
                        >
                          <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                        </Button>
                        {formData.prizeSendType === 1 &&
                          formData.prizeList[index].prizeType > 0 &&
                          !activityEditDisabled() && (
                            <Button
                              text
                              type="primary"
                              onClick={() => {
                                if (_.prizeType) {
                                  Dialog.confirm({
                                    v2: true,
                                    title: '提示',
                                    centered: true,
                                    content: '确认清空该奖品？',
                                    onOk: () => {
                                      formData.prizeList.splice(index, 1, PRIZE_INFO);
                                      setData(formData);
                                    },
                                    onCancel: () => console.log('cancel'),
                                  } as any);
                                }
                              }}
                            >
                              <i className={`iconfont icon-shanchu`} />
                            </Button>
                          )}
                        {formData.prizeSendType === 2 && !activityEditDisabled() && (
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              const newPlan = plan.filter((item) => item !== formData.prizeList[index].prizeKey);
                              setPlan(newPlan);
                              formData.prizeList.splice(index, 1);
                              setData(formData);
                            }}
                          >
                            <i className={`iconfont icon-shanchu`} />
                          </Button>
                        )}
                      </FormItem>
                    )}
                  />
                </Table>
                {formData.prizeSendType === 2 && (
                  <Button
                    type={'primary'}
                    disabled={activityEditDisabled() || formData.prizeList.length >= 4}
                    onClick={addPrize}
                    style={{ marginTop: 10 }}
                  >
                    添加奖项({formData.prizeList.length}/4)
                  </Button>
                )}
              </FormItem>
              {formData.prizeSendType === 2 && (
                <FormItem label="同一奖品" required>
                  <div className={styles['tips-box']}>最多领取一次</div>
                </FormItem>
              )}
            </>
          )}

          <LzDialog
            title={false}
            visible={visible}
            footer={false}
            onClose={() => setVisible(false)}
            style={{ width: '670px' }}
          >
            <ChoosePrize
              formData={formData}
              editValue={editValue}
              hasLimit={false}
              hasProbability={false}
              typeList={formData.prizeSendType === 1 ? [11] : [3]}
              defaultTarget={formData.prizeSendType === 1 ? 11 : 3}
              onChange={onPrizeChange}
              onCancel={onCancel}
              planList={plan}
              promoType={['10']}
              defaultEditValue={defaultValue.prizeList[target] as any}
              width={500}
              height={500}
              prizeNameLength={25}
            />
          </LzDialog>
        </Form>
      </LzPanel>
    </div>
  );
};
