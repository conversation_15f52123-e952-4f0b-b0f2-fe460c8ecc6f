import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  ifPlan: number;
  prizeKey: string;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  // 权益每日发放数量是否限制，1-不限制2-限制
  dayLimitType: number;
  // 权益每日发放上限
  dayLimit: number;
  // 奖品生效状态  1有效 0无效
  status: number;
}

export interface sectionSkuList {
  skuId: string;
  // 罐数
  skuPotNum: number;
  skuName: string;
  skuMainPicture: string;
  // 阶段排序
  sectionSort: number;
  // 阶段
  sectionType: string;
  // sku顺序
  sort: number;
}

export interface SectionList {
  // 阶段名称
  sectionName: string;
  // 罐数
  potNum: number;
  // 阶段排序
  sectionSort: number;
  // 阶段资产
  sectionPrizeList: PrizeInfo[];
  // 前置阶段sku
  beforeSkuList: sectionSkuList[];
  // 后置阶段sku
  afterSkuList: sectionSkuList[];
}

export interface CustomValue {
  pageBg: string;
  actBg: string;
  actBgColor: string;
  ruleBtn: string;
  myPrizeBtn: string;
  stepBg: string;
  prizeBg: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
}

export interface PageData {
  shopName: string;
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  crowdBag: any;
  // 前置订单前推天数
  days: number;
  // 是否延迟发奖
  isDelayedDisttribution: number;
  // 延迟发奖天数
  awardDays: number;
  // 限制订单状态 0-已付款 1-已完成
  orderRestrainStatus: number;
  // 阶段列表
  sectionList: SectionList[];
  fileName: string;
  fileList: [];
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/320268/39/9707/350552/68511a74F2a8d0ae7/fccb7ae2a906d0ef.png',
  actBg: '',
  actBgColor: '#fdeab9',
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/290275/29/13200/3754/68511a75F8731435e/bf96d3e71b42cada.png',
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/320444/15/9770/3916/68511a74F336c6523/00689a5e39ad5f31.png',
  stepBg: '//img10.360buyimg.com/imgzone/jfs/t1/293828/29/15018/76813/68511a73Faf2bb866/05112d311e563e95.png',
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/291509/11/14358/12028/68511a75F307c5f09/9a5a92b391a0e67f.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/279026/32/9061/41157/67e1284dF767f99ad/ecf68271ad90ef6d.jpg',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/279142/30/9023/5263/67e1284eF8311ac7b/713b3a7cfbacc716.jpg',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/274190/1/9056/48480/67e1284eF7dfff0a8/0aa0802faa26ce5c.jpg'
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  const now = (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00:59');
  const after30 = (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59');
  return {
    // 店铺名称
    shopName: getShop().shopName,
    // 活动名称
    activityName: `会员转段赠好礼-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [now, after30],
    // 活动开始时间
    startTime: format.formatDateTimeDayjs(now),
    // 活动结束时间
    endTime: format.formatDateTimeDayjs(after30),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '会员转段赠好礼，超多惊喜大奖等你来领！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    // 模板code
    templateCode: '',
    // 会员等级标签
    gradeLabel: [],
    // 人群包
    crowdBag: '',
    fileName: '',
    fileList: [],
    // 限制订单状态
    orderRestrainStatus: 1,
    // 是否延迟发奖
    isDelayedDisttribution: 0,
    // 延迟延迟发奖天数
    awardDays: 0,
    // 阶段列表
    sectionList: [],
    // 前置订单前推天数
    days: 180,
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  ifPlan: 0,
  prizeKey: '',
  startTime: '',
  endTime: '',
  startDate: '',
  endDate: '',
  dayLimit: 0,
  dayLimitType: 0,
  status: 1,
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

// const hasPrize = (formData: PageData): boolean => {
//   if (!formData.sectionList.length) {
//     Message.error('请上传系列数据');
//     return false;
//   }
//   for (let i = 0; i < formData.sectionPrizeList.length; i++) {
//     if (!formData.sectionPrizeList[i].periodName) {
//       Message.error(`请填写奖项${i + 1}阶段名称`);
//       return false;
//     }
//     if (!formData.sectionPrizeList[i].stepSetting) {
//       Message.error(`请选择奖项${i + 1}的阶段设置`);
//       return false;
//     }
//     if (!formData.sectionPrizeList[i].beforeOptions.length) {
//       Message.error(`请选择奖项${i + 1}转段前商品`);
//       return false;
//     }
//     if (!formData.sectionPrizeList[i].afterOptions) {
//       Message.error(`请选择奖项${i + 1}转段后商品`);
//       return false;
//     }
//     if (!formData.sectionPrizeList[i].prizeList.length) {
//       Message.error(`请上传奖项${i + 1}奖品`);
//       return false;
//     }
//     for (let j = 0; j < formData.sectionPrizeList[i].prizeList.length; j++) {
//       const prize = formData.sectionPrizeList[i].prizeList[j];
//       if (!prize.prizeName) {
//         Message.error(`请选择奖项${i + 1}的第${j + 1}个奖品`);
//         return false;
//       }
//       if (!isPrizeValid(prize, formData)) {
//         return false;
//       }
//     }
//   }
//   return true;
// };

export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType() && +getParams('status') === 2) {
    return true;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  if (!formData.sectionList.length) {
    Message.error('请上传阶段数据');
    return false;
  }
  // if (!hasPrize(formData)) {
  //   return false;
  // }

  return true;
};
