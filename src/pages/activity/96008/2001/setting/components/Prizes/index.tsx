import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import {
  Form,
  Field,
  Table,
  Button,
  Dialog,
  Input,
  Tab,
  Radio,
  Checkbox,
} from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrizeForDZ';
import { activityEditDisabled, deepCopy } from '@/utils';
import { FormLayout, PRIZE_TYPE, PRIZE_INFO } from '../../../util';
// 当前编辑行数据 除资产外 附加信息
const MAX_SERIES_PRIZE_LIST_LENGTH = 4;
const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
const RadioGroup = Radio.Group;
export default ({ sRef, onChange, value, defaultValue }) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  // 当前编辑的表格
  const [tableName, setTableName] = useState('');
  const [skuVisible, setSkuVisible] = useState(false);
  const [seriesSkuList, setSeriesSkuList] = useState([]);
  const [activeKey, setActiveKey] = useState('0');

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    formData.sectionList[activeKey].sectionPrizeList[target] = { ...data };
    setData(formData);
    setVisible(false);
  };

  useEffect((): void => {
    setFormData(value);
  }, [value]);

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors: any): void => {
        console.log('errors', errors);
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };

  const addPrizeSetting = () => {
    formData.sectionList.push({
      sectionName: '',
      sectionType: '',
      beforeOptions: [],
      afterOptions: [],
      sectionPrizeList: [],
      beforeSkuList: [],
      afterSkuList: [],
    });
    setData(formData);
  };

  return (
    <div>
      <LzPanel title="阶段商品及奖品设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <Button
            style={{ width: '100%' }}
            type="secondary"
            onClick={addPrizeSetting}
            disabled={
              formData.sectionList.length >= MAX_SERIES_PRIZE_LIST_LENGTH ||
              !formData.fileList.length ||
              activityEditDisabled()
            }
          >
            增加阶段配置 ({formData.sectionList.length}/{MAX_SERIES_PRIZE_LIST_LENGTH})
          </Button>
          <Tab
            activeKey={activeKey}
            onChange={(key) => setActiveKey(key)}
            unmountInactiveTabs
            triggerType={'click'}
            onClose={(key) => {
              Dialog.confirm({
                v2: true,
                title: '提示',
                centered: true,
                content: ' 删除该阶段项配置',
                onOk: () => {
                  formData.sectionList.splice(key, 1);
                  setActiveKey('0');
                  setData(formData);
                },
                onCancel: () => {},
              } as any);
            }}
          >
            {formData.sectionList.map((item, TabIndex) => {
              return (
                <Tab.Item
                  closeable={activityEditDisabled() ? false : TabIndex > 0}
                  title={` 阶段项${TabIndex + 1}  `}
                  key={TabIndex}
                >
                  <Form {...formItemLayout} field={field}>
                    <FormItem required label="阶段">
                      <Input
                        disabled={activityEditDisabled()}
                        value={item?.sectionType}
                        name={`sectionType-${TabIndex}`}
                        maxLength={10}
                        showLimitHint
                        className="w-300"
                        onChange={(sectionType) => {
                          item.sectionType = sectionType;
                          setData(formData);
                        }}
                        readOnly
                      />
                    </FormItem>
                    <FormItem required label="阶段标题" requiredMessage={'请输入阶段标题'}>
                      <Input
                        disabled={activityEditDisabled()}
                        value={item?.sectionName}
                        placeholder="请输入阶段标题"
                        name={`sectionName-${TabIndex}`}
                        maxLength={10}
                        showLimitHint
                        className="w-300"
                        onChange={(sectionName) => {
                          item.sectionName = sectionName;
                          setData(formData);
                        }}
                      />
                    </FormItem>
                    <FormItem
                      name={`beforeOptions-${TabIndex}`}
                      required
                      label="转段前商品"
                      requiredMessage={'请选择转段前商品'}
                    >
                      <div>
                        <Checkbox.Group
                          disabled={activityEditDisabled()}
                          onChange={(beforeOptions) => {
                            console.log('beforeOptions', beforeOptions);
                            item.beforeOptions = beforeOptions;
                            // item.afterOptions = beforeOptions.length > 0 ? item.afterOptions : '';
                            // item.afterOptions =
                            //   beforeOptions.length == formData.sectionList.length ? '' : item.afterOptions;
                            item.afterOptions =
                              Math.max(...item.beforeOptions, 0) >= item.afterOptions ? '' : item.afterOptions;
                            item.beforeSkuList = beforeOptions.reduce((acc, cur) => {
                              const skuList = formData.sectionList.find((option) => option.sectionSort === cur)?.skuList;
                              if (skuList) {
                                acc.push(...skuList);
                              }
                              return acc;
                            }, []);
                            setData(formData);
                          }}
                          name={`beforeOptions-${TabIndex}`}
                          value={item.beforeOptions}
                        >
                          {formData.sectionList.map((option, index) => (
                            <Checkbox
                              disabled={index === formData.sectionList.length - 1}
                              key={option.sectionSort}
                              value={option.sectionSort}
                            >
                              {option.sectionType}
                            </Checkbox>
                          ))}
                        </Checkbox.Group>
                        <Button
                          disabled={item.beforeOptions?.length === 0}
                          onClick={() => {
                            setSeriesSkuList(item?.beforeSkuList);
                            setSkuVisible(true);
                          }}
                          style={{ marginTop: 8 }}
                        >
                          查看SKU
                        </Button>
                      </div>
                    </FormItem>
                    <FormItem
                      required
                      label="转段后商品"
                      requiredMessage={'请选择转段后商品'}
                      name={`afterOptions-${TabIndex}`}
                    >
                      <RadioGroup
                        disabled={activityEditDisabled()}
                        name={`afterOptions-${TabIndex}`}
                        onChange={(afterOptions) => {
                          item.afterOptions = afterOptions;
                          item.afterSkuList = formData.sectionList.find(
                            (option) => option.sectionSort === afterOptions,
                          ).skuList;
                          item.previewSkuList = formData.sectionList.find(
                            (option) => option.sectionSort === afterOptions,
                          ).previewSkuList;
                          setData(formData);
                        }}
                        value={item.afterOptions}
                      >
                        {formData.sectionList.map((option) => (
                          <Radio
                            key={option.sectionSort}
                            value={option.sectionSort}
                            disabled={
                              item.beforeOptions?.length === 0 ||
                              Math.max(...item.beforeOptions, 0) >= option.sectionSort
                            }
                          >
                            {option.sectionType}
                          </Radio>
                        ))}
                        <Button
                          disabled={item.afterOptions?.length === 0}
                          onClick={() => {
                            setSeriesSkuList(item.afterSkuList);
                            setSkuVisible(true);
                          }}
                        >
                          查看SKU
                        </Button>
                      </RadioGroup>
                    </FormItem>
                    <FormItem required label="奖品列表">
                      <FormItem>
                        <Table dataSource={item?.sectionPrizeList} style={{ marginTop: '15px' }}>
                          <Table.Column title="奖品名称" dataIndex="prizeName" />
                          <Table.Column
                            title="奖品类型"
                            cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                            dataIndex="prizeType"
                          />
                          <Table.Column
                            title="单位数量"
                            cell={(_, index, row) => {
                              if (row.prizeType === 1) {
                                return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                              } else {
                                return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                              }
                            }}
                          />
                          <Table.Column
                            title="发放份数"
                            cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}` : ''}</div>}
                          />
                          <Table.Column
                            title="单份价值(元)"
                            cell={(_, index, row) => (
                              <div>{row.prizeName ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ''}</div>
                            )}
                          />
                          <Table.Column
                            title="每日发放限额"
                            cell={(_, index, row) => <div>{row.dayLimitType === 2 ? row.dayLimit : '不限'}</div>}
                          />

                          <Table.Column
                            title="操作"
                            width={130}
                            cell={(val, index, _) => (
                              <FormItem>
                                <Button
                                  text
                                  type="primary"
                                  onClick={() => {
                                    let row = item.sectionPrizeList[index];
                                    if (row.prizeName === '') {
                                      row = null;
                                    }
                                    setEditValue(row);
                                    setTarget(index);
                                    setTableName('sectionPrizeList');
                                    setVisible(true);
                                  }}
                                >
                                  <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                                </Button>
                                {formData.sectionList[activeKey].sectionPrizeList.length > 1 && (
                                  <Button
                                    text
                                    type="primary"
                                    disabled={activityEditDisabled()}
                                    onClick={() => {
                                      Dialog.confirm({
                                        v2: true,
                                        title: '提示',
                                        centered: true,
                                        content: '确认删除该奖品？',
                                        onOk: () => {
                                          item.sectionPrizeList.splice(index, 1);
                                          setData(formData);
                                        },
                                        onCancel: () => console.log('cancel'),
                                      } as any);
                                    }}
                                  >
                                    <i className={`iconfont icon-shanchu`} />
                                  </Button>
                                )}
                              </FormItem>
                            )}
                          />
                        </Table>
                      </FormItem>
                      <FormItem>
                        <Button
                          disabled={item?.sectionPrizeList?.length >= (activityEditDisabled() ? 6 : 1)}
                          type="primary"
                          onClick={() => {
                            item?.sectionPrizeList?.push(deepCopy({ ...PRIZE_INFO }));
                            setData(formData);
                          }}
                        >
                          +添加奖品（{item.sectionPrizeList?.length}/1）
                        </Button>
                      </FormItem>
                    </FormItem>
                  </Form>
                </Tab.Item>
              );
            })}
          </Tab>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          defaultEditValue={defaultValue?.sectionList[activeKey]?.sectionPrizeList[target] ?? null}
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasProbability={false}
          hasLimit={true}
          hasShowTime
          typeList={[1, 2, 3, 4, 6, 8]}
          defaultTarget={2}
        />
      </LzDialog>
      {/*  sku列表 */}
      <LzDialog
        title={false}
        visible={skuVisible}
        footer={false}
        onClose={() => setSkuVisible(false)}
        style={{ width: '670px', height: '500' }}
      >
        {seriesSkuList.length && (
          <div style={{ margin: '10px' }}>
            <Table dataSource={seriesSkuList} fixedHeader>
              <Table.Column title="阶段" dataIndex="sectionType" />
              <Table.Column title="罐数" dataIndex="potNum" />
              <Table.Column title="商品id" dataIndex="skuId" />
            </Table>
          </div>
        )}
      </LzDialog>
    </div>
  );
};
