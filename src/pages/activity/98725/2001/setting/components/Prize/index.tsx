/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-06 14:58
 * Description:
 */
import React, {useReducer, useImperativeHandle, useEffect, useState} from 'react';
import {Form, Field, NumberPicker, Button, Table, Dialog, Radio, Grid, Input} from '@alifd/next';
import styles from '../../style.module.scss';
import LzPanel from '@/components/LzPanel';
import {activityEditDisabled, getParams} from '@/utils';
import {FormLayout, PageData} from '../../../util';
import ChoosePromotion from './components/ChoosePromotion';
import LzImageSelector from '@/components/LzImageSelector';
import {urlRegularCheck} from "../../../../../../decorateNew/utils";

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({onChange, defaultValue, value, sRef}: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return {...p, ...c};
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  const field: Field = Field.useField();
  const [show, setShow] = useState(false);
  const [editValue, setEditValue] = useState<any>();

  const setData = (data): void => {
    setFormData(data);
    onChange({...formData, ...data});
  };

  const Disabled = () => {
    return activityEditDisabled() || getParams('type') === 'edit';
  };

  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    if (data) {
      formData.prizeList[0] = data;
    } else {
      formData.prizeList = [];
    }
    setData(formData);
  };

  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  return (
    <div>
      <LzPanel title="奖项限制">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="奖品名称" required requiredMessage="请输入奖品名称">
            <Input
              value={formData.prizeName}
              placeholder="请输入奖品名称"
              name="prizeName"
              maxLength={20}
              showLimitHint
              className="w-300"
              onChange={(prizeName) => setData({prizeName})}
            />
          </FormItem>
          <FormItem label="兑换奖品所需积分" required requiredMessage="请输入兑换奖品所需积分" name="points">
            <NumberPicker
              value={formData.points}
              onChange={(points: number) => setData({points})}
              type="inline"
              min={1}
              max={9999999}
              className={styles.number}
            />
            积分
          </FormItem>
          <FormItem label="锁权图片" required>
            <Grid.Row>
              <Form.Item style={{marginRight: 10, marginBottom: 0}}>
                <LzImageSelector
                  width={689}
                  value={formData.lockImage}
                  onChange={(lockImage) => {
                    setData({lockImage});
                  }}
                />
              </Form.Item>
              <Form.Item style={{marginBottom: 0}}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度689px、推荐图片高度314px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setData({lockImage: ''});
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </FormItem>
          <FormItem label="奖品图片" required>
            <Grid.Row>
              <Form.Item style={{marginRight: 10, marginBottom: 0}}>
                <LzImageSelector
                  width={686}
                  value={formData.prizeImage}
                  onChange={(prizeImage) => {
                    setData({prizeImage});
                  }}
                />
              </Form.Item>
              <Form.Item style={{marginBottom: 0}}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度686px、推荐图片高度900px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setData({prizeImage: ''});
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </FormItem>
          <FormItem
            required
            label="奖品链接"
            name="prizeLink"
            requiredMessage={'请输入热区链接'}
            validator={urlRegularCheck}
          >
            <Input
              name="prizeLink"
              className={
                styles.input}
              value={formData.prizeLink}
              placeholder={'请输入奖品链接'}
              onFocus={() => {
              }}
              onChange={(prizeLink) => {
                setData({prizeLink});
              }}
            />
          </FormItem>
          <FormItem label="选择奖品令牌" required>
            {formData.prizeList.length > 0 && (
              <Table dataSource={formData.prizeList} style={{marginTop: '15px'}}>
                <Table.Column title="令牌名称" dataIndex="prizeName"/>
                <Table.Column
                  title="单位数量"
                  cell={(_, index, row) => {
                    if (row.prizeType === 1) {
                      return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                    } else {
                      return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                    }
                  }}
                />
                <Table.Column
                  title="发放总数"
                  cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                />
                <Table.Column
                  title="单份价值(元)"
                  cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
                />

                {!activityEditDisabled() && (
                  <Table.Column
                    title="操作"
                    width={130}
                    cell={(val, index, _) => (
                      <div>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            const row = formData.prizeList[index];
                            setEditValue(row);
                            setShow(true);
                          }}
                        >
                          <i className={`iconfont icon-chaojiyingxiaoicon-25`}/>
                        </Button>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认删除该令牌？',
                              onOk: () => {
                                formData.prizeList = [];
                                setData(formData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }}
                        >
                          <i className={`iconfont icon-shanchu`}/>
                        </Button>
                      </div>
                    )}
                  />
                )}
              </Table>
            )}
            {formData.prizeList.length <= 0 && (
              <Button
                type="primary"
                onClick={() => {
                  setShow(true);
                  setEditValue({});
                }}
              >
                选择令牌
              </Button>
            )}
          </FormItem>
        </Form>
      </LzPanel>

      <ChoosePromotion
        promoType={'1'}
        show={show}
        submit={onPrizeChange}
        setShow={() => setShow(false)}
        value={formData.prizeList[0] ?? null}
        disabled={Disabled()}
      />
    </div>
  );
};
