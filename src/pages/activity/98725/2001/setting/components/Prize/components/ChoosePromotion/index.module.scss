$ERROR_COLOR: #f33;
$WARNING_COLOR: #f90;
$SUCCESS_COLOR: #0b6;
$INFO_COLOR: #39f;
.container {
  width: 350px;
  height: 100px;
  border: 1px solid lightgray;
  color: gray;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  &:hover {
    border: 1px solid gray;
    color: #5b5a5a;
  }
}

.headerTitle {
  display: flex;
  height: 40px;
  align-items: center;
  background: #f0f2f5;
  padding-left: 15px;

  > span {
    margin-right: 8px;
  }
}

.coupon {
  display: flex;
  border: 1px solid lightgray;
  padding: 10px;
  color: #4d4c4c;
  width: 450px;
  position: relative;
  cursor: pointer;
  flex-direction: column;

  .info {
    display: flex;
  }

  &:hover {
    .couponBg {
      display: flex;
    }

    .couponDel {
      display: block;
    }
  }
}

.couponDel {
  display: none;
}

.couponBg {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, .3);
  z-index: 9999999;
  justify-content: center;
  align-items: center;

}

.couponLeft {
  > div {
    margin-top: 5px;
  }
}

.couponRight {
  margin-left: 15px;

  > div {
    line-height: 19px;
  }
}

.part1 {
  display: flex;
  justify-content: flex-start;
}

.part1_p1 {
  display: -webkit-box;
  width: 100%;
  margin: 0;
  overflow: hidden;
  line-height: 1.5 !important;
  text-align: left;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.part1_p2 {
  margin: 0;
  color: #8e97a2;
  text-align: left;
}

.PropertyJdBeanPlan {
  width: 1000px;

  .reminderBox {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .totalStyle {
    color: #9ca7b6;
  }

  .table-status-WARNING_COLOR {
    // 黄色字体
    color: $WARNING_COLOR;
  }

  .table-status-INFO_COLOR {
    // 蓝色字体
    color: $INFO_COLOR;
  }

  .table-status-SUCCESS_COLOR {
    // 绿色字体
    color: $SUCCESS_COLOR;
  }

  .table-status-ERROR_COLOR {
    // 红色字体
    color: $ERROR_COLOR;
  }

  .pagination {
    margin-top: 20px;
  }
}
