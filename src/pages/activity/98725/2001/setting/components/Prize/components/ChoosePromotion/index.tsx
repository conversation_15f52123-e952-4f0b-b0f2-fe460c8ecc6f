import React, {useState} from 'react';
import styles from './index.module.scss';
import dayjs from 'dayjs';
import {Dialog, Button} from '@alifd/next';
import Plan from './components/Plan';

// eslint-disable-next-line complexity
export default (props) => {
  const {submit, value, show, disabled, promoType = '', setShow} = props;
  // const [show, setShow] = useState(false);

  const onSubmit = (prizeInfo) => {
    setShow(false);
    prizeInfo.prizeType = 11;
    const pirze = {
      itemId: '1',
      prizeImg: '1',
      prizeKey: String(prizeInfo.tokenId),
      prizeName: prizeInfo.name,
      prizeType: 11,
      sendTotalCount: 1,
      sortId: 1,
      unitCount: 1,
      unitPrice: 1,
      tokenId: prizeInfo.secretKey,
      beginTime: prizeInfo.beginTime,
      endTime: prizeInfo.endTime,
    };
    submit(pirze);
  };

  return (
    <Dialog title="选择令牌" shouldUpdatePosition footer={false} visible={show} onClose={() => setShow(false)}>
      <Plan promoType={promoType} onSubmit={onSubmit} value={value}/>
    </Dialog>

  );
};
