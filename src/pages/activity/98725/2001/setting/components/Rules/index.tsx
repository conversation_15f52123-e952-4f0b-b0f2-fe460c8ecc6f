/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-06 15:48
 * Description:
 */

import React, {useImperativeHandle, useReducer, useEffect} from 'react';
import {Form, Button, Field, Input} from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import {checkActivityData, FormLayout, PageData, PrizeInfo} from '../../../util';
import format from '@/utils/format';
import {isPopShop} from '@/utils';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
  checkForm: () => boolean;
}

export default ({onChange, defaultValue, value, sRef, checkForm}: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return {...p, ...c};
  }, value || defaultValue);
  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const setData = (data): void => {
    setFormData(data);
    onChange({...formData, ...data});
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  /**
   * 自动生成规则说明
   */
  const autoCreateRuleDesc = async (): Promise<void> => {
    if (!checkForm()) return;
    const isValidateData: boolean = checkActivityData(formData);
    if (!isValidateData) {
      return;
    }
    const rules = `活动说明
参与活动前请认真阅读活动规则，参与本次活动即视为用户同意并接受本规则的全部内容。
活动举办方
本活动由【润百颜京东自营官方旗舰店】举办。

一、活动时间
1、活动时间：${format.formatDateTimeDayjs(formData.startTime)}至${format.formatDateTimeDayjs(
      formData.endTime,
    )}
2、锁权时间：${format.formatDateTimeDayjs(formData.lockStartTime)}至${format.formatDateTimeDayjs(
      formData.lockEndTime,
    )}
3、下单时间：${format.formatDateTimeDayjs(formData.orderStartTime)}至${format.formatDateTimeDayjs(
      formData.orderEndTime,
    )}

二、活动店铺
【润百颜京东自营官方旗舰店】

三、活动规则
1、活动期间：618期间消耗${formData.points}积分拍下锁权礼包，除此时间外不可参与锁权活动，锁权礼包数量有限，先到先得；
2、锁权礼包内含：${formData.prizeName}；
3、购买锁权礼包后，在规定时间${format.formatDateTimeDayjs(formData.orderStartTime)}至${format.formatDateTimeDayjs(
      formData.orderEndTime,
    )}内，购指定正装商品，单笔实付款满${formData.minGmv}元并于活动结束前确认收货，订单完成且确认收货${formData.overDays}天后（无退款行为）即可进行权益领取，每个ID限领1次，数量有限。
4、此${formData.points}积分锁权虚拟商品链接用于锁定优惠购资格，一经购买，不支持退换；权益领取后为专享价，若专享价订单退款，不退回已领取的权益。
5、${formData.points}积分虚拟商品链接无法单独展现在APP订单信息中，并且客服端无法查询订单信息。

四、其他活动细则
1、本次活动所有奖品均不允许折现、非质量问题不退换，不开具发票。
2、由于技术系统的限制，节日或大促等流量高峰期间您参与活动和订单判断可能会发生延迟，如系统无法进入，届时您将暂时无法参与，我们建议您稍后再试，具体所需时间请以实际情况为准。
3、活动期间，若出现参与者通过不正当手段、不诚信方式参与活动的（包括但不限于机器作弊、恶意套取、刷信誉、虛假交易、扰乱系统、实施网络攻击等），本店铺有权单方面取消其抽奖参与资格及所获奖品；若奖品已发出，本店铺有权要求中奖者退回奖品，或在中奖者拒绝或无法退回奖品的情况下在向中奖者的退款（如有） 中扣除奖品价值；
4、所有奖品仅限中奖者本人领取。主办方有权在任何时候对中奖者的中奖资格进行复核。如中奖者存在任何不符合中奖条件的情形（包括但不限于中奖者的订单发生仅退款/退货退款等导致中奖者不再符合获取中奖资格的前提条件），均视为其主动放弃相应的抽奖参与资格及奖品，主办方有权不予提供奖品；
5、如遇不可抗力、政府管制（包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整的等）、网络传输故障、系统发生故障或遭受第三方攻击及其他主办方无法控制的情形，在法律允许的范围内主办方对前述情形所导致的一切后果不承担任何责任，并有权相应地取消、终止、修改、暂停或延迟本次活动；
6、本店铺可以根据本活动的实际举办情况对活动规则进行变动或调整，相关变动或调整将公布在活动页面上，公布后依法生效；
7、如对本次活动有任何其他问题，请详询在线客服。
`;
    setData({rules});
    field.setErrors({rules: ''});
  };
  return (
    <div>
      <LzPanel title="活动规则">
        <Form {...formItemLayout} field={field}>
          <FormItem label="规则内容" required requiredMessage="请生成/输入规则内容">
            <Input.TextArea
              value={formData.rules}
              name="rules"
              onChange={(rules) => setData({rules})}
              autoHeight={{minRows: 8, maxRows: 40}}
              placeholder="请输入活动规则说明"
              maxLength={5000}
              showLimitHint
              className="form-input-ctrl"
            />
          </FormItem>
          <FormItem label=" " colon={false}>
            <Button
              type="primary"
              className="table-cell-btn"
              onClick={autoCreateRuleDesc}
              style={{marginRight: '15px'}}
              text
            >
              自动生成规则说明
            </Button>
            <span style={{color: 'red', fontSize: '12px'}}>
              提示：点击按钮自动生成规则，也可以自己编辑信息，手机端规则处显示。
            </span>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
