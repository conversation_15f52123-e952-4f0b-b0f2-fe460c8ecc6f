/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-02 11:30
 * Description:
 */
import React, {useReducer, useImperativeHandle, useEffect, useState} from 'react';
import {Form, Field, Table, Button, Dialog, Message, Radio, NumberPicker, DatePicker2, Input} from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import {PageData, FormLayout} from '../../../util';
import {activityEditDisabled, validateActivityThreshold} from '@/utils';
import styles from '../../style.module.scss';
import dayJs from 'dayjs';
import SkuList from '@/components/SkuList';
import dayjs from "dayjs";
import format from '@/utils/format';
import constant from '@/utils/constant';
import ChooseGoods from '@/components/ChooseGoods';
import LzTip from "@/components/LzTip";

const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const RadioGroup = Radio.Group;
const {RangePicker} = DatePicker2;
const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>, string) => void;
}

export default ({onChange, defaultValue, value, sRef}: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return {...p, ...c};
  }, value || defaultValue);

  const [goodDialog, setGoodDialog] = useState(false);
  const [currentGood, setCurrentGood] = useState<any>([]);

  useEffect(() => {
    if (!formData.lockStartTime) return;
    field.validate('powerRangeData');
  }, [formData.lockStartTime]);

  useEffect(() => {
    if (formData.lockStartTime) {
      field.validate('powerRangeData');
    }
  }, [formData.rangeDate]);

  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({...formData, ...data}, 'task');
  };

  const handleSkuChange = (data) => {
    formData.skuList = data;
    setData({
      skuList: data
    })
  };

  const onPowerRangeChange = (powerRangeDate): void => {
    setData({
      lockStartTime: dayjs(powerRangeDate[0]).format(dateFormat),
      lockEndTime: dayjs(powerRangeDate[1]).format(dateFormat),
    });
  };

  const showGoods = (good): void => {
    console.log(good);
    setCurrentGood(good);
    setGoodDialog(true);
  };

  // 锁权时间校验
  const powerRangeDataValidator = (rule: any, val: any, callback: any) => {
    if (dayjs(formData.startTime).valueOf() > dayjs(val[0]).valueOf()) {
      callback('锁权开始时间应大于活动开始时间');
    } else if (dayjs(val[1]).valueOf() > dayjs(formData.endTime).valueOf()) {
      callback('锁权结束时间应小于活动结束时间');
    } else {
      callback();
    }
  };

  const checkOrderRangeDate = (rule: any, value: any, callback: any) => {
    if (!value[0] || !value[1]) {
      callback('请选择下单时间');
      return;
    }
    callback('');
  };

  const onOrderRangeChange = (orderRangeDate): void => {
    setFormData({
      orderStartTime: format.formatDateTimeDayjs(orderRangeDate[0]),
      orderEndTime: format.formatDateTimeDayjs(orderRangeDate[1]),
    });
  };

  /**
   * 添加任务组件成功回调
   * 向任务列表中添加数据 & 同步数据
   * @param taskInfo 任务信息
   */
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      // return err;
      return validateActivityThreshold(formData, err, field);
    },
  }));
  return (
    <div>
      <LzPanel title="订单规则">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <Form.Item label="锁权时间" required requiredMessage="请选择锁权时间" validator={powerRangeDataValidator}>
            <RangePicker
              className="w-300"
              name="powerRangeData"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={[formData.lockStartTime, formData.lockEndTime]}
              onChange={onPowerRangeChange}
              disabledDate={(date) => {
                return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
              }}
            />
          </Form.Item>

          <Form.Item label="下单时间" required requiredMessage="请选择下单时间" validator={checkOrderRangeDate}>
            <RangePicker
              className="w-300"
              name="orderRangeDate"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={[formData.orderStartTime, formData.orderEndTime]}
              onChange={onOrderRangeChange}
              disabledDate={(date) => {
                return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
              }}
            />
          </Form.Item>
          <Form.Item
            name="overDays"
            required
            requiredTrigger="onBlur"
            label="订单限制天数"
            requiredMessage="请输入订单限制天数"
          >
            <NumberPicker
              value={formData.overDays}
              onChange={(overDays: number) => setData({overDays})}
              type="inline"
              min={0}
              max={15}
              className={styles.number}
            />
            <div className={styles.tip}>
              限制商品确认收货后{formData.overDays}
              天未退款才可参与兑换，且活动时间要包含锁权时间、下单时间+（订单限制天数）、兑换时间。
            </div>
          </Form.Item>
          <Form.Item label="订单金额" required>
            <Form.Item required requiredTrigger="onBlur" requiredMessage="请输入总订单金额" style={{margin: 0}}>
              大于等于
              <NumberPicker
                name="minGmv"
                value={formData.minGmv}
                onChange={(minGmv: number) => setData({minGmv})}
                type="inline"
                min={1}
                max={9999999}
                precision={2}
                className={styles.number}
              />
              元
            </Form.Item>
          </Form.Item>
          <Form.Item label="订单状态" required>
            <RadioGroup value={1}>
              <Radio id="1" value={1}>
                已完成
              </Radio>
            </RadioGroup>
          </Form.Item>

          <Form.Item label="订单商品" required>
            <div>
              {!activityEditDisabled() &&
                <ChooseGoods value={formData.skuList} onChange={handleSkuChange}/>}

              {formData.skuList?.length > 0 &&
                <Button text type="primary" onClick={() => showGoods(formData.skuList)}>
                  查看商品
                </Button>
              }
              <LzTip>
                选择指定商品后，活动也将展示指定商品，不展示曝光商品，不要填写影子分身SKU，否则会造成用户无法参加活动
              </LzTip>
            </div>
          </Form.Item>
        </Form>

        <Dialog
          width={822}
          v2
          title="查看商品"
          visible={goodDialog}
          footer={false}
          onClose={() => setGoodDialog(false)}
          onOk={() => setGoodDialog(false)}
        >
          <div className={styles.showGoods}>
            <SkuList skuList={currentGood}/>
          </div>
        </Dialog>
      </LzPanel>
    </div>
  );
};
