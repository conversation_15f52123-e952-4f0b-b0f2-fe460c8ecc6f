.setting {
  margin-top: 15px;
}

.panel {
  box-sizing: border-box;
  background: #f5f7f9;
  padding: 15px;
  border: 1px solid #d7dde4;
  min-width: 350px;
}
.number {
  margin: 0 10px;
}
.tip {
  font-size: 12px;
  color: gray;
  margin-top: 15px;
}
.tips {
  font-size: 12px;
}

.container {
  width: 350px;
  height: 100px;
  border: 1px solid lightgray;
  color: gray;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  &:hover {
    border: 1px solid gray;
    color: #5b5a5a;
  }
}

.showGoods {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 10px;

  .skuContainer {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid lightgray;

    .skuImg {
      width: 80px;
      height: 80px;
      margin-right: 8px;
    }

    .skuName {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .skuId {
      color: lightgray;
      font-size: 12px;
    }

    .price {
      color: red;
      font-weight: bold;
      margin-bottom: 5px;
    }
  }
}
