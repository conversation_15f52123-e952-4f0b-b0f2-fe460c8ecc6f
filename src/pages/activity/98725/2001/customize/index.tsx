/**
 * Author: zhang<PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 设置模板
 */
import React, {useState} from 'react';
import {CustomValue} from '../util';
import LzTipPanel from '@/components/LzTipPanel';
// 基础信息
import Base from './components/Base';
import Prize from './components/Prize';
import Step from './components/Step';
import PopupImg from './components/PopupImg';
import {Tab} from '@alifd/next';

interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  handleChange: (formData: Required<CustomValue>) => void;
  target: number;
  showPopup: (type: boolean) => void;
}

interface EventProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<CustomValue>) => void;
}

export default (props: Props) => {
  const {target, defaultValue, value, handleChange, showPopup} = props;
  const [activeKey, setActiveKey] = useState('1');
  const handleTabChange = (data) => {
    setActiveKey(data);
    showPopup(data === '2');
  };
  // 各装修自组件抛出的数据
  // 只负责转发，不作处理
  const onChange = (formData): void => {
    handleChange(formData);
  };
  const eventProps: EventProps = {
    defaultValue,
    value,
    onChange,
  };
  return (
    <div>
      <LzTipPanel message="自定义模板时，点击样例图的各区域，进行素材的更换"/>
      <Tab activeKey={activeKey} defaultActiveKey="1" onChange={handleTabChange}>
        <Tab.Item title="活动主页" key="1"/>
        <Tab.Item title="弹窗" key="2"/>
      </Tab>
      {activeKey === '1' && (
        <>
          {<Base {...eventProps} />}
          {<Step {...eventProps} />}
        </>
      )}
      {activeKey === '2' && <PopupImg {...eventProps} />}
    </div>
  );
};
