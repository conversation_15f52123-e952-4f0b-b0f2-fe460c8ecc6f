/**
 * Author: lin<PERSON><PERSON>
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, {Dayjs} from 'dayjs';
import {Message} from '@alifd/next';
import format from '@/utils/format';
import {getParams} from '@/utils';
import {getShop} from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
}

export interface CustomValue {
  pageBg: string;
  actBg: string;
  actBgColor: string;
  btnColor: string;
  btnBg: string;
  btnBorderColor: string;
  giftImg: string;
  stepFlow: string;
  successPopup: string;
  sureLockRightPopup: string;
  sureExchangePopup: string;
  popupLink: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  hotZoneList: any[];
  shopNameColor: string;
  disableShopName: number;
}

export interface PageData {
  shopName: string;
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  orderStartTime: string,
  orderEndTime: string,
  lockStartTime: string,
  lockEndTime: string,
  threshold: number;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  skuList: any;
  minGmv: string;
  prizeName: string;
  lockImage: string;
  prizeImage: string;
  prizeLink: string;
  points: number;
  prizeList: any;
  overDays: number;
  disableShopName: number,
}

// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  pageBg: '',
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/309317/16/715/87900/68243552F3ec68062/36dc4f7742a201c8.jpg}', // 主页背景图
  actBgColor: '', // 主页背景色
  btnColor: '', // 按钮字体颜色
  btnBg: '', // 按钮背景颜色
  btnBorderColor: '', // 按钮边框颜色
  giftImg: '',
  stepFlow: '//img10.360buyimg.com/imgzone/jfs/t1/312605/16/765/88764/68243551F56d43407/aabfc196edaf9d09.png',
  successPopup: '//img10.360buyimg.com/imgzone/jfs/t1/289858/10/12939/41373/68513ac1Fbafb674b/f99902fc3b67275f.png',
  sureLockRightPopup: '//img10.360buyimg.com/imgzone/jfs/t1/291850/36/15010/39137/68513ac2F40c4cec3/d364af58417ed033.png',
  sureExchangePopup: '//img10.360buyimg.com/imgzone/jfs/t1/299584/9/15662/34505/68513ac1Fde45381b/727cad498711145c.png',
  popupLink: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
  hotZoneList: [],
  shopNameColor: '', // 店铺名称颜色
  disableShopName: 0,
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    disableShopName: 0,
    // 店铺名称
    shopName: getShop().shopName,
    // 活动名称
    activityName: `积分锁权-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 下单时间
    orderStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    orderEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 锁权时间
    lockStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    lockEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '积分锁权 甄享礼遇',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    // 曝光商品列表
    skuList: [],
    // 总订单金额
    minGmv: '',
    prizeName: '',
    lockImage: '//img10.360buyimg.com/imgzone/jfs/t1/284384/19/28739/21472/681192c7Fb6b5987d/1deb41d9bc9bea00.png',
    prizeImage: '//img10.360buyimg.com/imgzone/jfs/t1/300565/13/1007/69051/681192c9F4866b577/bf3ebe75c9fef101.png',
    prizeLink: '',
    points: 10,
    prizeList: [],
    overDays: 7,
  };
};

// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};
// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    return '店铺会员'
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));
  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

// 校验订单规则
const isOrderValid = (formatData: PageData): boolean => {
  if (dayjs(formatData.orderStartTime).isBefore(dayjs(formatData.startTime))) {
    Message.error('下单时间不能早于活动开始时间');
    return false;
  }
  if (dayjs(formatData.orderStartTime).isAfter(dayjs(formatData.endTime))) {
    Message.error('下单时间不能晚于活动结束时间');
    return false;
  }
  return true;
};

// eslint-disable-next-line complexity
export const checkActivityData = (formData: PageData): boolean => {
  // 非编辑模式校验开始时间
  if (!isProcessingEditType()) {
    // 开始时间异常
    if (!isStartTimeValid(formData.startTime)) {
      return false;
    }
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  if (formData.skuList.length <= 0) {
    Message.error('请选择参与活动的订单商品');
    return false;
  }
  if (!isOrderValid(formData)) {
    return false;
  }
  if(!formData.lockImage){
    Message.error('请上传锁权图片');
    return false;
  }
  if(!formData.prizeImage){
    Message.error('请奖品图片');
    return false;
  }
  // if (!formData.prizeList.length) {
  //   Message.error('请选择奖品令牌');
  //   return false;
  // }
  return true;
};
