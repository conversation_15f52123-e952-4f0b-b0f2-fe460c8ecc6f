/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer, useState } from 'react';
import { Form, Table, Input, Button, Dialog } from '@alifd/next';
  import { formItemLayout, generateMembershipString, PageData } from '../util';
import dayjs from 'dayjs';
import styles from './style.module.scss';
import format from '@/utils/format';
// import LzImg from '@/components/LzImg';
// import CONST from '@/utils/constant';
import SkuList from '@/components/SkuList';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const [goodDialog, setGoodDialog] = useState(false);
  const [currentGood, setCurrentGood] = useState<any>([]);
  const showGoods = (good): void => {
    console.log(good);
    setCurrentGood(good);
    setGoodDialog(true);
  };

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.startTime)}至${format.formatDateTimeDayjs(
          formData.endTime,
        )}`}</FormItem>
        <FormItem label="活动门槛">{generateMembershipString(formData)}</FormItem>
        <FormItem label="锁权时间">
          {dayjs(formData. lockStartTime).format('YYYY-MM-DD HH:mm:ss')}至
          {dayjs(formData. lockEndTime).format('YYYY-MM-DD HH:mm:ss')}
        </FormItem>
        <FormItem label="下单时间">
          {dayjs(formData.orderStartTime).format('YYYY-MM-DD HH:mm:ss')}至
          {dayjs(formData.orderEndTime).format('YYYY-MM-DD HH:mm:ss')}
        </FormItem>
        <FormItem label="订单限制天数">{formData.overDays}天</FormItem>
        <FormItem label="订单金额">大于等于{formData.minGmv}元</FormItem>
        <FormItem label="订单状态">已完成</FormItem>

        <FormItem label="兑换奖品所需积分">{formData.points}</FormItem>
        <FormItem label="锁权奖品">
          <Table dataSource={formData.prizeList} style={{ marginTop: '15px' }}>
            <Table.Column title="奖项名称" dataIndex="prizeName" />
            <Table.Column
              title="单位数量"
              cell={(_, index, row) => {
                if (row.prizeType === 1) {
                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                } else {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }
              }}
            />
            <Table.Column
              title="发放总数"
              cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
            />
            <Table.Column
              title="单份价值(元)"
              cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
            />
          </Table>
        </FormItem>
        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>

      <Dialog
        width={822}
        v2
        title="查看商品"
        visible={goodDialog}
        footer={false}
        onClose={() => setGoodDialog(false)}
        onOk={() => setGoodDialog(false)}
      >
        <div className={styles.showGoods}>
          <SkuList skuList={currentGood} />
          {/* {currentGood?.map((sku) => { */}
          {/*  return ( */}
          {/*    <div className={styles.skuContainer}> */}
          {/*      <img className={styles.skuImg} src={sku.skuMainPicture} alt="" /> */}
          {/*      <div> */}
          {/*        <div className={styles.skuName}>{sku.skuName}</div> */}
          {/*        <div className={styles.skuId}>SKUID:{sku.skuId}</div> */}
          {/*        <div className={styles.price}>¥ {sku.jdPrice}</div> */}
          {/*      </div> */}
          {/*    </div> */}
          {/*  ); */}
          {/* })} */}
        </div>
      </Dialog>
    </div>
  );
};
