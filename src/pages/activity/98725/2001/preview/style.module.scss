.preview {
  .value {
    display: flex;
    align-items: center;
  }
}

.part1 {
  display: flex;
  justify-content: flex-start;
}

.part1_p1 {
  display: -webkit-box;
  width: 100%;
  margin: 0;
  overflow: hidden;
  line-height: 1.5 !important;
  text-align: left;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.part1_p2 {
  margin: 0;
  color: #8e97a2;
  text-align: left;
}

.showGoods {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 10px;

  .skuContainer {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid lightgray;

    .skuImg {
      width: 80px;
      height: 80px;
      margin-right: 8px;
    }

    .skuName {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .skuId {
      color: lightgray;
      font-size: 12px;
    }

    .price {
      color: red;
      font-weight: bold;
      margin-bottom: 5px;
    }
  }
}
