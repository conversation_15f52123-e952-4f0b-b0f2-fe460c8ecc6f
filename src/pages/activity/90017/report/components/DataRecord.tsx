import React, { useEffect, useState } from 'react';
import { Form, DatePicker2, Field, Table, Button } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataReport, dataReportExport } from '@/api/v90017';
import { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataReport(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataReportExport(formValue).then((data: any) => downloadExcel(data, `${getParams('name')}活动数据`));
  };

  // 动态的列
  const aliveColumns = () => {
    if (tableData.length > 0 && tableData[0].ladderPrizeInfo) {
      return tableData[0].ladderPrizeInfo.flatMap((ladder, index1) => {
        if (ladder.prizeInfoVosList?.length > 0) {
          return (
            <Table.ColumnGroup
              key={`ladder-${index1}`}
              title={`${ladder.ladderName}奖品数据`}
              style={{ textAlign: 'center' }}
            >
              {ladder.prizeInfoVosList.map((prize, index2) => (
                <Table.Column
                  width={200}
                  key={`prize-${prize.prizeName}-${index2}`}
                  align={'center'}
                  title={`${prize.prizeName}奖品领取人数`}
                  dataIndex={`${prize.receiveNum}`}
                  cell={() => <span>{prize.receiveNum}</span>}
                />
              ))}
            </Table.ColumnGroup>
          );
        }
        return null;
      });
    }
    return null;
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="查询时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column width={200} align={'center'} lock={'left'} title="日期" dataIndex="dt" />
        <Table.Column width={200} align={'center'} title="PV" dataIndex="pv" />
        <Table.Column width={200} align={'center'} title="UV" dataIndex="uv" />
        <Table.Column width={200} align={'center'} title="发起人数" dataIndex="leaderCount" />
        <Table.Column width={200} align={'center'} title="助力人数" dataIndex="helpCount" />
        <Table.Column width={200} align={'center'} title="新开卡入会人数" dataIndex="inMember" />
        <Table.Column width={200} align={'center'} title="邀新首购人数" dataIndex="helpOrderUser" />
        <Table.Column width={200} align={'center'} title="邀新首购金额" dataIndex="helpOrderAmt" />
        <Table.Column width={200} align={'center'} title="发起人-领奖人数" dataIndex="leaderReceiveUser" />
        <Table.Column width={200} align={'center'} title="被邀请者-领奖人数" dataIndex="newReceiveUser" />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
