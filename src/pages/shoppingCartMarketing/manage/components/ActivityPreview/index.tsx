import React, { lazy, Suspense } from 'react';
import LzEmpty from '@/components/LzEmpty';
// import { previewActivities } from '@/routers/activity';
const previewActivities = [
  { path: '/activity/shoppingCartMarketing/view/preview', component: lazy(() => import('@/pages/shoppingCartMarketing/view/preview')) },
];
/**
 * 异步导入各活动预览组建
 * @param data 活动数据
 * @return JSX｜null
 */
const AsyncComponents = ({ data }): React.ReactElement | null => {
  console.log(`@/pages/activity/shoppingCartMarketing/view/preview`);
  const DynamicComponent = previewActivities.find((item) => {
    return item.path === `/activity/shoppingCartMarketing/view/preview`;
  })?.component;

  if (DynamicComponent) {
    return (
      <div>
        <DynamicComponent defaultValue={data} labelAlign="top" value={data} />
      </div>
    );
  } else {
    console.error('v1版本活动，未匹配到组件');
    return <LzEmpty />;
  }
};

export default ({ data }) => {
  return (
    <Suspense fallback={<div>加载中...</div>}>
      <div>
        <AsyncComponents data={data} />
      </div>
    </Suspense>
  );
};
