import React, { forwardRef, useEffect, useState } from 'react';
import { Checkbox, Divider, Icon, Overlay, Button } from '@alifd/next';
import { getVenderLevelRule } from '@/api/common';
import styles from './index.module.scss';

const GradeSelector = forwardRef((props: any, _ref) => {
  const { value, onChange, disabled = false, applyGrade = [-10] } = props;
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [memberLevels, setMemberLevels] = useState([]);
  const [indeterminate, setIndeterminate] = React.useState(false);
  const [checkAll, setCheckAll] = React.useState(true);
  const [checkedList, setCheckedList] = React.useState([]);
  const [curRef, setCurRef] = React.useState<any>();
  const [extraList, setExtraList] = React.useState<any>([]);
  const checkGroup = [{ label: '对应等级及以上', value: -12 }];

  const emit = (list, clist) => {
    list = list.filter((e) => e > 0);
    const nameList = list
      .sort((a, b) => {
        return a - b;
      })
      .map((id) => {
        const level: any = memberLevels.find((m: any) => m.customerLevel === id);
        return level?.customerLevelName || `${id}星会员`;
      });
    if (clist.length) {
      // eslint-disable-next-line array-callback-return
      const cnameList = checkGroup.map((e: any) => {
        if (clist.includes(e.value)) {
          return e.label;
        }
      });
      const name = [...nameList, ...cnameList].filter((e) => e);
      onChange([...new Set([...list, ...clist])].join(','), name);
    } else {
      onChange(list.join(','), nameList);
    }
  };

  const onCheckGroupChange = (list) => {
    list = list.filter((e) => e);
    setCheckedList(list);
    setIndeterminate(!!list.length && list.length < memberLevels.length);
    setCheckAll(list.length === memberLevels.length);
    emit(list, extraList);
  };

  const onCheckAllChange = (checked, e) => {
    const clist: any = e.target.checked ? memberLevels.map((item: any) => item.customerLevel) : [];
    setCheckedList(clist);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
    emit(clist, extraList);
  };

  // const onFollowerChange = (checked) => {
  //   setFollowChecked(checked);
  //   emit(
  //     checkedList.filter((e) => e),
  //     checked,
  //   );
  // };
  const onExtraChange = (list) => {
    setExtraList(list);
    emit(checkedList, list);
  };

  const getData = () => {
    setLoading(true);
    getVenderLevelRule()
      .then((data) => {
        setMemberLevels(data as any);
        if (!data.length) {
          setCheckAll(false);
          setVisible(true);
        }
        const values = value.split(',').map((v) => Number(v));
        const extraLevel = values.filter((v) => v < 0);
        const shopLevel = values.filter((v) => v >= 0);
        let level = data.map((e) => e.customerLevel);
        level = [...new Set([...values, ...extraLevel])];
        if (!shopLevel.length) {
          setCheckAll(false);
        }
        // eslint-disable-next-line array-callback-return
        const nameList = data.map((e: any) => {
          if (level.includes(e.customerLevel)) {
            return e.customerLevelName;
          }
        }); // eslint-disable-next-line array-callback-return
        const extraNameList = checkGroup.map((e: any) => {
          if (extraLevel.includes(e.value)) {
            return e.label;
          }
        });
        const name = [...nameList, ...extraNameList].filter((e) => e);
        onChange(level.join(','), name);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getData();
  }, []);

  useEffect(() => {
    const values = value.split(',').map((v) => Number(v));
    const shopLevel = values.filter((v) => v > 0).map((o) => Number(o));
    const extraLevel = values.filter((v) => v < 0);
    setCheckedList(shopLevel);
    setExtraList(extraLevel);
  }, [value]);

  return (
    <div>
      <Checkbox
        disabled={disabled}
        value={0}
        indeterminate={indeterminate}
        onChange={onCheckAllChange}
        checked={checkAll}
        ref={(ref) => {
          setCurRef(ref);
        }}
      >
        店铺会员
      </Checkbox>
      <Overlay v2 visible={visible} safeNode={() => curRef} target={() => curRef}>
        <div style={{ width: '500px' }}>
          <div className={[styles.tipPanel, styles.warnColor].join(' ')}>
            <i className={['iconfont', 'icon-icon-30', styles.icon].join(' ')} />
            <div>当前店铺暂未开通会员体系，需前往京东后台开通后再设置</div>
            <span>
              <Button
                text
                style={{ marginLeft: 15 }}
                type="primary"
                onClick={() => {
                  window.open('https://shop.jd.com/jdm/mkt/member/newmemberlevel', '_blank');
                }}
              >
                前往开通
              </Button>
            </span>
          </div>
        </div>
      </Overlay>
      <Checkbox.Group disabled={disabled} value={checkedList} onChange={onCheckGroupChange}>
        {memberLevels &&
          memberLevels.map((item: any) => (
            <Checkbox key={item.customerLevel} value={item.customerLevel}>
              {item.customerLevelName}
            </Checkbox>
          ))}
      </Checkbox.Group>
      {loading && <Icon type="loading" style={{ color: '#39f', marginLeft: '10px' }} />}
      <Divider dashed />
      <Checkbox.Group disabled={disabled} value={extraList} onChange={onExtraChange}>
        {checkGroup.map((item) => {
          if (applyGrade.includes(item.value)) {
            return (
              <Checkbox key={item.value} value={item.value}>
                {item.label}
              </Checkbox>
            );
          }
          return null;
        })}
      </Checkbox.Group>
    </div>
  );
});

export default GradeSelector;
