import React from 'react';
import { Tag } from '@alifd/next';

const { Group: TagGroup } = Tag;

const presetColors = ['blue', 'green', 'orange', 'red', 'turquoise', 'yellow'];

export default ({ tags }) => {
  return (
    <div className="tag-list">
      <TagGroup>
        {tags.map((tag, index) => (
          <Tag key={tag} size={'small'} color={presetColors[index]}>
            {tag}
          </Tag>
        ))}
      </TagGroup>
    </div>
  );
};
