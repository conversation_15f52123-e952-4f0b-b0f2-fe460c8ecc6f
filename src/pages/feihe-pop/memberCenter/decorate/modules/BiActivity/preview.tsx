import React from 'react';
import styles from './index.module.scss';
import { Slider } from '@alifd/next';
export default ({ data, dispatch, activityActiveTab }) => {
  return (
    <div className={styles.preview}>
      <div className={styles['member-benefits-container']}>
        <div
          className={styles.title}
          style={{
            backgroundImage: `url(${data.memberBenefitTitleImg})`,
          }}
        ></div>
        {/* 查看领取记录 */}
        <div className={styles['look-exchange-record']}>{`查看领取权益>`}</div>
        {/* BI展示的tips */}
        <div className={styles['member-benefits-tips']} style={{ backgroundColor: `${data.memberBenefitTipsColor}` }}>
          <img
            className={styles.icon}
            src="https://img10.360buyimg.com/imgzone/jfs/t1/121771/5/39507/414/65ae7309Fff46f848/13bc3e3a8f04f0cc.png"
          />
          <div className={styles['tips-scroll-container']}>
            <div className={styles['tips-text']}>{data.biActivity[activityActiveTab.biTab].tips}</div>
          </div>
        </div>
        {/* BI展示的活动 */}
        {data.biActivity[activityActiveTab.biTab].list.length < 4 && (
          <div className={styles['member-benefits-activity']}>
            {data.biActivity[activityActiveTab.biTab].list.map((item, index) => (
              <img
                key={index}
                className={`${styles['member-benefits-activity-link']} ${
                  styles[`member-benefits-activity-link-length-${data.biActivity[activityActiveTab.biTab].list.length}`]
                } ${item ? '' : styles.skeleton}`}
                src={item?.img}
              />
            ))}
          </div>
        )}
        {data.biActivity[activityActiveTab.biTab].list.length > 3 && (
          <div style={{ width: '100%', height: '86px' }}>
            <Slider slidesToShow={3} arrowPosition="inner" dots={false} arrows={false} autoplay={true} lazyLoad>
              {data.biActivity[activityActiveTab.biTab].list.map((item, index) => (
                <div className={styles['member-benefits-activity-slider']} key={index}>
                  <img
                    key={index}
                    className={`${styles['member-benefits-activity-link']} ${
                      styles['member-benefits-activity-link-length-3']
                    } ${item ? '' : styles.skeleton}`}
                    src={item?.img}
                  />
                </div>
              ))}
            </Slider>
          </div>
        )}
      </div>
    </div>
  );
};
