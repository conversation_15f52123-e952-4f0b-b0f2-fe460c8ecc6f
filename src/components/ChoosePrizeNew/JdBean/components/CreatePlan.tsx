import styles from '../index.module.scss';
import { Button, Form, Input, DatePicker2, Message, NumberPicker, Loading } from '@alifd/next';
import * as React from 'react';
import { ComponentProps, CreateJdBeanPlan, CreateJdBeanPlanRule, CreateJdBeanPlanSubmitForm } from '../types';
import { addResPrizeBean } from '@/api/prize';

export default ({ handleCancel, handleSubmit }: ComponentProps) => {
  const { RangePicker } = DatePicker2;
  const [loading, setLoading] = React.useState<boolean>(false);
  const [price, setPrice] = React.useState<number>(0);
  const formItemLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  };

  // 校验时间
  const checkPutDate = (rule: CreateJdBeanPlanRule, value: any[], callback: Function) => {
    if (value[1] === null) {
      callback('请选择投放结束时间');
    } else {
      callback();
    }
  };
  // 提交创建京豆表单
  const onOkCreate = (value: CreateJdBeanPlan, errors: unknown): void | boolean => {
    if (!errors) {
      const valueForm: CreateJdBeanPlanSubmitForm = {
        ...value,
        sendRule: 1,
        // 京豆计划发放每人次限制
        sendTimesPerUser: 20,
        startDate: value.putTime[0].format('YYYY-MM-DD HH:mm:ss'),
        endDate: value.putTime[1].format('YYYY-MM-DD HH:mm:ss'),
      };
      if (new Date(valueForm.startDate).getTime() < new Date().getTime()) {
        Message.error('投放起始时间要大于当前时间');
        return false;
      }
      setLoading(true);
      addResPrizeBean(valueForm)
        .then((res) => {
          Message.success('创建成功');
          setLoading(false);
          handleSubmit();
        })
        .catch((err) => {
          Message.error(err.message);
          setLoading(false);
        });
    }
  };
  // 关闭弹窗
  const onCloseCreate = () => {
    handleCancel();
  };

  return (
    <div className={styles.CreateJdBeanPlan}>
      <Loading visible={loading}>
        <Form {...formItemLayout} style={{ width: 600 }}>
          <Form.Item label="计划名称：" required className={styles.item} requiredMessage="请输入计划名称">
            <Input trim placeholder="请输入计划名称" maxLength={20} showLimitHint name="planName" />
          </Form.Item>
          <Form.Item
            label="投放时间："
            required
            className="bean_btn"
            requiredMessage="请选择投放时间"
            validator={checkPutDate}
          >
            <RangePicker showTime name="putTime" />
          </Form.Item>
          <Form.Item label="计划内容：" className={styles.item}>
            <Input.TextArea placeholder="" maxLength={200} showLimitHint name="planContent" />
          </Form.Item>
          <Form.Item
            label="京豆预算额："
            required
            requiredMessage="请输入京豆预算额"
            extra={<span style={{ marginLeft: '10px' }}>(价值 {(price / 100).toFixed(2)}元)</span>}
          >
            <NumberPicker
              placeholder="请输入京豆预算额"
              style={{ width: 200 }}
              name="quantityTotal"
              max={9999999999}
              min={1}
              onChange={(value) => setPrice(value)}
            />
          </Form.Item>
          <Form.Item label=" ">
            <Form.Submit validate type="primary" className={styles.form_btn} onClick={onOkCreate}>
              提交
            </Form.Submit>
            <Button className={styles.form_btn} onClick={onCloseCreate}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </Loading>
    </div>
  );
};
