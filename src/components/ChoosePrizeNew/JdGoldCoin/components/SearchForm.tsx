import { useState, useEffect } from 'react';
import * as React from 'react';
import { Button, Form, Input, CascaderSelect } from '@alifd/next';
import { getAllFuluEquityCategoryList } from '@/api/prize';
import { GetFuluCateListResponse } from '@/api/types';

export default ({ onSearch }: any) => {
  const [treeData, setTreeData] = useState<GetFuluCateListResponse[]>([]);
  const [treeValue, setTreeValue] = useState<string[]>([]);
  const [planName, setPlanName] = useState<string>('');
  const [planId, setPlanId] = useState<string>('');
  const [cate, setCate] = useState<any>({});
  const onSearchClick = () => {
    onSearch({
      planName,
      planId,
      ...cate,
    });
  };
  const handleChange = (value, data, extra) => {
    const [productCateIdFirst, productCateIdSecond, productCateId] = extra.selectedPath.map((e) => e.value);
    setTreeValue(value);
    setCate({
      productCateIdFirst,
      productCateIdSecond,
      productCateId,
    });
  };

  const onResetClick = () => {
    setTreeValue([]);
    setPlanId('');
    setPlanName('');
    setCate({
      productCateIdFirst: '',
      productCateIdSecond: '',
      productCateId: '',
    });
    onSearch({
      planName: '',
      planId: '',
      productCateIdFirst: '',
      productCateIdSecond: '',
      productCateId: '',
    });
  };

  const updateCateIdToName = (data) => {
    const updatedData = data.map((item) => {
      return {
        ...item,
        value: item.cateId,
        label: item.cateName,
        children: item.child ? updateCateIdToName(item.child) : null,
      };
    });
    return updatedData;
  };

  const fetchEquityCategoryData = () => {
    getAllFuluEquityCategoryList().then((res) => {
      const result = updateCateIdToName(res);
      setTreeData(result);
    });
  };

  useEffect(() => {
    fetchEquityCategoryData();
  }, []);
  return (
    <div>
      <Form inline>
        <Form.Item className="item" label="计划名称：">
          <Input
            value={planName}
            className="dialog-search-ctrl"
            placeholder="请输入计划名称"
            onChange={(v) => setPlanName(v)}
          />
        </Form.Item>
        <Form.Item className="item" label="计划编号：">
          <Input
            value={planId}
            className="dialog-search-ctrl"
            placeholder="请输入计划编号"
            onChange={(v) => {
              const numericValue = v.replace(/\D/g, '');
              setPlanId(numericValue);
            }}
          />
        </Form.Item>
        <Form.Item className="item" label="权益分类：">
          <CascaderSelect defaultExpandedValue={[]} value={treeValue} dataSource={treeData} onChange={handleChange} />
        </Form.Item>
        <Form.Item className="item" style={{ textAlign: 'right' }}>
          <Button type="primary" onClick={onSearchClick}>
            查询
          </Button>
          <Button style={{ marginLeft: 10 }} type="normal" onClick={onResetClick}>
            重置
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};
